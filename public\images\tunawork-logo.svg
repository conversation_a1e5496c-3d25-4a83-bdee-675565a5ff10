<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="tunaworkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stopColor="#3B82F6"/>
      <stop offset="100%" stopColor="#2563EB"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" floodColor="#3B82F6" floodOpacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <rect width="40" height="40" rx="12" fill="url(#tunaworkGradient)" filter="url(#shadow)"/>
  
  <!-- Letter T -->
  <g transform="translate(8, 8)">
    <!-- Top bar of T -->
    <rect x="2" y="3" width="20" height="3" rx="1.5" fill="white"/>
    <!-- Vertical bar of T -->
    <rect x="10" y="3" width="4" height="18" rx="2" fill="white"/>
    
    <!-- Small accent dot -->
    <circle cx="12" cy="24" r="1.5" fill="white" opacity="0.8"/>
  </g>
</svg> 