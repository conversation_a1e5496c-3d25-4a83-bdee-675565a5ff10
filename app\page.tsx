"use client";

import { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { Logo } from "@/src/components/ui/logo";
import {
  ChevronRight,
  Smartphone,
  Globe,
  Award,
  Users,
  FileText,
  FolderTree,
  Sparkles,
  TrendingUp,
  Target,
  Heart,
  ArrowRight,
  ChevronLeft,
  ArrowUp,
  ArrowDown,
  Mail,
  Phone,
  Linkedin,
} from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/src/components/ui/lib/utils";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Mousewheel, Keyboard } from "swiper/modules";
import type { Swiper as SwiperType } from "swiper";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import {
  FeaturesSlide,
  FinancialSlide,
  HeroSlide,
  MarketSlide,
  ProblemSlide,
  RevenueSlide,
  SolutionSlide,
  FundingSlide,
  ImpactSlide,
  MissionSlide,
  ContactSlide,
} from "@/src/components/slides";
import { SlideNavigation } from "@/src/components/navigation/slide-navigation";
import { MobileNavigation } from "@/src/components/navigation/mobile-navigation";
import { DesktopNavigation } from "@/src/components/navigation/desktop-navigation";

export default function Home() {
  const [activeSlide, setActiveSlide] = useState(0);
  const [showNavigation, setShowNavigation] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const swiperRef = useRef<SwiperType | null>(null);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const handleSlideChange = (direction: "prev" | "next") => {
    if (swiperRef.current) {
      if (direction === "prev") {
        swiperRef.current.slidePrev();
      } else {
        swiperRef.current.slideNext();
      }
    }
  };

  const goToSlide = (index: number) => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(index);
    }
  };

  return (
    <main className="h-screen w-screen overflow-hidden relative text-gray-800">
      {/* Background with elegant white theme */}
      <div className="fixed inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-blue-100" />
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2260%22 height=%2260%22 viewBox=%220 0 60 60%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%233b82f6%22 fill-opacity=%220.03%22%3E%3Ccircle cx=%2230%22 cy=%2230%22 r=%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_rgba(59,130,246,0.1)_0%,_transparent_70%)] pointer-events-none" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,_rgba(59,130,246,0.05)_0%,_transparent_60%)] pointer-events-none" />
      </div>

      {/* Navigation Toggle Button */}
      <motion.button
        onClick={() => setShowNavigation(!showNavigation)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="fixed top-4 left-4 z-[60] glassmorphism backdrop-blur-md border border-blue-200/30 shadow-lg rounded-xl p-3 cursor-pointer"
        initial={{ x: -200, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <div className="flex items-center gap-2">
          {showNavigation ? (
            <ChevronLeft className="h-4 w-4 text-blue-600" />
          ) : (
            <ChevronRight className="h-4 w-4 text-blue-600" />
          )}
          <Logo size="sm" showText={false} />
        </div>
      </motion.button>

      {/* Desktop Navigation Arrows */}
      {!isMobile && (
        <DesktopNavigation
          activeSlide={activeSlide}
          handleSlideChange={handleSlideChange}
        />
      )}

      {/* Mobile Navigation Indicators */}
      {isMobile && <MobileNavigation />}

      {/* Slide Navigation Draggable à gauche */}
      <SlideNavigation
        activeSlide={activeSlide}
        goToSlide={goToSlide}
        handleSlideChange={handleSlideChange}
        showNavigation={showNavigation}
      />

      {/* Swiper Slides */}
      <Swiper
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
        spaceBetween={0}
        slidesPerView={1}
        direction="horizontal"
        mousewheel={!isMobile}
        keyboard={{ enabled: true }}
        allowTouchMove={true}
        pagination={{
          clickable: true,
          dynamicBullets: true,
        }}
        modules={[Navigation, Pagination, Mousewheel, Keyboard]}
        className="h-screen w-screen"
        onSlideChange={(swiper) => setActiveSlide(swiper.activeIndex)}
      >
        {/* Hero Slide */}
        <SwiperSlide className="h-full w-full">
          <HeroSlide goToSlide={goToSlide} />
        </SwiperSlide>

        {/* Problem Slide */}
        <SwiperSlide className="h-full w-full">
          <ProblemSlide />
        </SwiperSlide>

        {/* Solution Slide */}
        <SwiperSlide className="h-full w-full">
          <SolutionSlide />
        </SwiperSlide>

        {/* Market Size Slide */}
        <SwiperSlide className="h-full w-full">
          <MarketSlide />
        </SwiperSlide>

        {/* Features Slide */}
        <SwiperSlide className="h-full w-full">
          <FeaturesSlide />
        </SwiperSlide>

        {/* Revenue Model Slide */}
        <SwiperSlide className="h-full w-full">
          <RevenueSlide />
        </SwiperSlide>

        {/* Financial Projections Slide */}
        <SwiperSlide className="h-full w-full">
          <FinancialSlide />
        </SwiperSlide>

        {/* Funding Slide */}
        <SwiperSlide className="h-full w-full">
          <FundingSlide />
        </SwiperSlide>

        {/* Impact Slide */}
        <SwiperSlide className="h-full w-full">
          <ImpactSlide />
        </SwiperSlide>

        {/* Mission Slide */}
        <SwiperSlide className="h-full w-full">
          <MissionSlide />
        </SwiperSlide>

        {/* Contact Slide */}
        <SwiperSlide className="h-full w-full">
          <ContactSlide />
        </SwiperSlide>
      </Swiper>
    </main>
  );
}
