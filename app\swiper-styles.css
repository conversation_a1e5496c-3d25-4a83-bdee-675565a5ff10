/* Styles personnalisés pour Swiper */

/* Assurez-vous que le slide prend toute la hauteur de l'écran */
.swiper {
  height: 100vh;
  width: 100%;
}

.swiper-slide {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
  scrollbar-width: none; /* Pour Firefox */
}

.swiper-slide::-webkit-scrollbar {
  display: none; /* Pour Chrome, Safari et Opera */
}

/* Styles pour les puces de pagination */
.swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background: rgba(59, 130, 246, 0.3);
  opacity: 0.6;
  transition: all 0.3s ease;
  margin: 0 4px !important;
}

.swiper-pagination-bullet-active {
  background: #3b82f6 !important; /* Couleur blue-500 */
  opacity: 1 !important;
  transform: scale(1.5);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

.swiper-pagination {
  bottom: 20px !important;
}

/* Styles pour les boutons de navigation */
.swiper-button-next,
.swiper-button-prev {
  color: #10b981;
  background: rgba(0, 0, 0, 0.3);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(1.1);
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 20px;
}

/* Styles pour le défilement vertical */
.swiper-scrollbar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  height: 8px;
}

.swiper-scrollbar-drag {
  background: rgba(16, 185, 129, 0.7);
  border-radius: 10px;
}

/* Styles responsifs */
@media (max-width: 768px) {
  .swiper-button-next,
  .swiper-button-prev {
    width: 40px;
    height: 40px;
    display: none; /* Cache les boutons sur mobile */
  }

  .swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    margin: 0 3px !important;
  }

  .swiper-pagination-bullet-active {
    transform: scale(1.8);
  }

  .swiper-pagination {
    bottom: 80px !important; /* Plus haut pour éviter la navigation mobile */
  }

  /* Améliore la zone tactile */
  .swiper-slide {
    touch-action: pan-y pinch-zoom;
  }

  /* Optimise le scroll vertical sur mobile */
  .swiper-slide {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }
}

/* Styles pour améliorer les performances tactiles */
.swiper-container {
  touch-action: pan-x pan-y;
}

/* Animation de feedback tactile */
@keyframes touchFeedback {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

.swiper-slide.swiper-slide-active {
  animation: touchFeedback 0.2s ease-out;
}
